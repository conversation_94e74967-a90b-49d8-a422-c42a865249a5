import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../stores'

const routes = [
  {
    path: '/',
    redirect: '/dashboard'
  },
  {
    path: '/login',
    component: () => import('../views/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/dashboard',
    component: () => import('../views/Dashboard.vue'),
    meta: {
      title: '仪表盘',
      icon: 'Monitor',
      requiresAuth: true
    }
  },
  {
    path: '/search-words',
    component: () => import('../views/SearchWords.vue'),
    meta: {
      title: '增加检索词',
      icon: 'Plus',
      requiresAuth: true
    }
  },
  {
    path: '/supply-list',
    component: () => import('../views/SupplyList.vue'),
    meta: {
      title: '供应列表',
      icon: 'List',
      requiresAuth: true
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 导航守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 初始化用户信息（从本地存储恢复）
  if (!userStore.isLoggedIn && localStorage.getItem('token')) {
    userStore.initUserInfo()
  }

  // 如果访问登录页面且已经登录，重定向到首页
  if (to.path === '/login' && userStore.isLoggedIn) {
    next('/dashboard')
    return
  }

  // 检查路由是否需要认证
  if (to.meta.requiresAuth) {
    if (!userStore.isLoggedIn) {
      // 未登录，重定向到登录页，并保存原始路径
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 已登录，但只在必要时验证token有效性
    // 避免每次路由切换都验证token，减少不必要的请求
    if (from.path === '/login' || !from.path) {
      // 只在从登录页跳转或首次访问时验证token
      const isTokenValid = await userStore.checkTokenValidity()
      if (!isTokenValid) {
        // token无效，重定向到登录页
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }
  }

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 供应管理系统`
  }

  next()
})

export default router
import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '../router'

// 动态获取API基础URL
const getApiBaseUrl = () => {
  // 获取当前访问的主机名和协议
  const hostname = window.location.hostname
  const protocol = window.location.protocol

  console.log('当前访问地址:', { hostname, protocol })

  // 如果是IP地址（局域网访问），使用相同的IP访问后端
  if (/^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    const apiUrl = `${protocol}//${hostname}:3000`
    console.log('使用IP地址访问后端:', apiUrl)
    return apiUrl
  }

  // 如果是localhost或127.0.0.1，使用localhost
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    const apiUrl = 'http://localhost:3000'
    console.log('使用localhost访问后端:', apiUrl)
    return apiUrl
  }

  // 其他情况，尝试使用当前主机名
  const apiUrl = `${protocol}//${hostname}:3000`
  console.log('使用当前主机名访问后端:', apiUrl)
  return apiUrl
}

// 创建axios实例
const request = axios.create({
  baseURL: getApiBaseUrl(), // 动态后端服务地址
  timeout: 15000, // 增加超时时间到15秒
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加调试信息
    console.log('发送请求:', {
      url: config.url,
      baseURL: config.baseURL,
      method: config.method,
      fullUrl: `${config.baseURL}${config.url}`
    })

    // 可以在这里添加token等认证信息
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const { code, message, data } = response.data
    
    // 如果响应中有code字段，按原来的逻辑处理
    if (code !== undefined) {
      if (code === 200) {
        return response.data  // 返回完整响应，包含code、data、message等
      } else {
        ElMessage.error(message || '请求失败')
        return Promise.reject(new Error(message || '请求失败'))
      }
    }
    
    // 如果没有code字段，直接返回data（兼容其他格式）
    return response.data
  },
  error => {
    console.error('响应错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          // 清除本地存储的认证信息
          localStorage.removeItem('token')
          localStorage.removeItem('userInfo')
          // 跳转到登录页，保存当前路径用于登录后重定向
          const currentPath = router.currentRoute.value.fullPath
          if (currentPath !== '/login') {
            router.push({
              path: '/login',
              query: { redirect: currentPath }
            })
          }
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络连接异常')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

export default request
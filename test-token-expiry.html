<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token过期测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .log { 
            max-height: 300px; 
            overflow-y: auto; 
            background: #000; 
            color: #0f0; 
            padding: 10px; 
            font-family: monospace; 
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Token过期处理测试</h1>
        
        <div class="test-item info">
            <h3>测试说明</h3>
            <p>此工具用于测试token过期时的处理机制，验证是否会出现无限循环请求。</p>
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>设置一个无效的token</li>
                <li>发送需要认证的请求</li>
                <li>观察是否会出现重复的401请求</li>
            </ol>
        </div>

        <div class="test-item">
            <h3>Token管理</h3>
            <button onclick="setValidToken()">设置有效Token</button>
            <button onclick="setInvalidToken()">设置无效Token</button>
            <button onclick="clearToken()">清除Token</button>
            <button onclick="showCurrentToken()">显示当前Token</button>
            <div id="tokenInfo"></div>
        </div>

        <div class="test-item">
            <h3>API测试</h3>
            <button onclick="testVerifyAPI()">测试验证API</button>
            <button onclick="testMultipleRequests()">测试多个并发请求</button>
            <button onclick="clearLog()">清除日志</button>
            <div id="apiResult"></div>
        </div>

        <div class="test-item">
            <h3>请求日志</h3>
            <div id="requestLog" class="log"></div>
        </div>
    </div>

    <script>
        let requestCount = 0;
        let logContainer = document.getElementById('requestLog');

        function log(message, type = 'info') {
            const timestamp = new Date().toISOString();
            const color = type === 'error' ? '#f00' : type === 'success' ? '#0f0' : '#fff';
            logContainer.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function setValidToken() {
            // 这里应该设置一个真实的有效token，但为了测试，我们使用一个假的
            localStorage.setItem('token', 'valid-token-example');
            log('设置有效Token', 'success');
            showCurrentToken();
        }

        function setInvalidToken() {
            localStorage.setItem('token', 'invalid-token-12345');
            log('设置无效Token', 'success');
            showCurrentToken();
        }

        function clearToken() {
            localStorage.removeItem('token');
            log('清除Token', 'success');
            showCurrentToken();
        }

        function showCurrentToken() {
            const token = localStorage.getItem('token');
            const tokenInfo = document.getElementById('tokenInfo');
            tokenInfo.innerHTML = `
                <div class="info">
                    <p><strong>当前Token:</strong> ${token || '无'}</p>
                </div>
            `;
        }

        async function testVerifyAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<p>正在测试验证API...</p>';
            
            requestCount = 0;
            log('开始测试验证API');

            const hostname = window.location.hostname;
            const protocol = window.location.protocol;
            let apiUrl;
            
            if (/^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
                apiUrl = `${protocol}//${hostname}:3000`;
            } else {
                apiUrl = 'http://localhost:3000';
            }

            try {
                const token = localStorage.getItem('token');
                const headers = {
                    'Content-Type': 'application/json'
                };
                
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                requestCount++;
                log(`发送请求 #${requestCount}: ${apiUrl}/api/auth/verify`);

                const response = await fetch(`${apiUrl}/api/auth/verify`, {
                    method: 'GET',
                    headers: headers
                });

                const data = await response.text();
                
                if (response.ok) {
                    log(`请求成功: ${response.status}`, 'success');
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ 验证API测试成功</h4>
                            <p><strong>状态码:</strong> ${response.status}</p>
                            <pre>${data}</pre>
                        </div>
                    `;
                } else {
                    log(`请求失败: ${response.status}`, 'error');
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ 验证API测试失败</h4>
                            <p><strong>状态码:</strong> ${response.status}</p>
                            <pre>${data}</pre>
                        </div>
                    `;
                }
            } catch (error) {
                log(`请求异常: ${error.message}`, 'error');
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 验证API测试异常</h4>
                        <p><strong>错误:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testMultipleRequests() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<p>正在测试多个并发请求...</p>';
            
            requestCount = 0;
            log('开始测试多个并发请求');

            const hostname = window.location.hostname;
            const protocol = window.location.protocol;
            let apiUrl;
            
            if (/^\d+\.\d+\.\d+$/.test(hostname)) {
                apiUrl = `${protocol}//${hostname}:3000`;
            } else {
                apiUrl = 'http://localhost:3000';
            }

            const token = localStorage.getItem('token');
            const headers = {
                'Content-Type': 'application/json'
            };
            
            if (token) {
                headers['Authorization'] = `Bearer ${token}`;
            }

            // 发送5个并发请求
            const promises = [];
            for (let i = 0; i < 5; i++) {
                promises.push(
                    fetch(`${apiUrl}/api/auth/verify`, {
                        method: 'GET',
                        headers: headers
                    }).then(response => {
                        requestCount++;
                        log(`并发请求 #${requestCount} 完成: ${response.status}`, 
                            response.ok ? 'success' : 'error');
                        return response;
                    }).catch(error => {
                        requestCount++;
                        log(`并发请求 #${requestCount} 异常: ${error.message}`, 'error');
                        throw error;
                    })
                );
            }

            try {
                const responses = await Promise.allSettled(promises);
                const successCount = responses.filter(r => r.status === 'fulfilled' && r.value.ok).length;
                const failCount = responses.length - successCount;

                resultDiv.innerHTML = `
                    <div class="${successCount > 0 ? 'success' : 'error'}">
                        <h4>📊 并发请求测试完成</h4>
                        <p><strong>成功:</strong> ${successCount}</p>
                        <p><strong>失败:</strong> ${failCount}</p>
                        <p><strong>总请求数:</strong> ${requestCount}</p>
                    </div>
                `;
            } catch (error) {
                log(`并发测试异常: ${error.message}`, 'error');
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ 并发请求测试异常</h4>
                        <p><strong>错误:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        function clearLog() {
            logContainer.innerHTML = '';
            log('日志已清除', 'success');
        }

        // 页面加载时显示当前token
        window.onload = function() {
            showCurrentToken();
            log('Token过期测试工具已加载', 'success');
        };
    </script>
</body>
</html>
